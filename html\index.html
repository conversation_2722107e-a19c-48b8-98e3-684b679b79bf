<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产计划管理系统</title>
    <style>
        /* 全局样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        /* 主容器 */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            min-height: 100vh;
        }

        /* 搜索表单区域 */
        .search-form {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .form-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group label {
            font-weight: 500;
            color: #495057;
            white-space: nowrap;
            min-width: 60px;
        }

        .form-group input {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            width: 150px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0056b3;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #545b62;
        }

        /* 时间筛选区域 */
        .date-filter {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .date-filter label {
            font-weight: 500;
            color: #495057;
        }

        .date-filter input[type="date"] {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .update-time {
            margin-left: auto;
            color: #6c757d;
            font-size: 14px;
        }

        /* 表格容器 */
        .table-container {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .data-table th {
            background-color: #f8f9fa;
            padding: 12px 8px;
            text-align: center;
            font-weight: 600;
            color: #495057;
            border: 1px solid #dee2e6;
            white-space: nowrap;
        }

        .data-table td {
            padding: 10px 8px;
            text-align: center;
            border: 1px solid #dee2e6;
            vertical-align: middle;
        }

        .data-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .data-table tbody tr:hover {
            background-color: #e9ecef;
        }

        /* 状态按钮样式 */
        .status-btn {
            padding: 4px 12px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            min-width: 60px;
        }

        .status-completed {
            background-color: #28a745;
            color: white;
        }

        .status-in-progress {
            background-color: #007bff;
            color: white;
        }

        .status-pending {
            background-color: #6c757d;
            color: white;
        }

        .status-not-started {
            background-color: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .status-started {
            background-color: #17a2b8;
            color: white;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
            border-top: 1px solid #e9ecef;
        }

        .pagination-info {
            color: #6c757d;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #dee2e6;
            background-color: white;
            color: #495057;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .page-btn:hover {
            background-color: #e9ecef;
        }

        .page-btn.active {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .container {
                padding: 15px;
            }

            .form-row {
                gap: 15px;
            }

            .form-group input {
                width: 120px;
            }
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .form-group {
                width: 100%;
            }

            .form-group input {
                width: 100%;
                max-width: 200px;
            }

            .date-filter {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .update-time {
                margin-left: 0;
            }

            .table-container {
                overflow-x: auto;
            }

            .pagination {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- 主容器 -->
    <div class="container">
        <!-- 搜索表单区域 -->
        <div class="search-form">
            <div class="form-row">
                <!-- 工单号输入框 -->
                <div class="form-group">
                    <label for="workOrder">工单号:</label>
                    <input type="text" id="workOrder" placeholder="请输入">
                </div>

                <!-- 批次号输入框 -->
                <div class="form-group">
                    <label for="batchNo">批次号:</label>
                    <input type="text" id="batchNo" placeholder="请输入">
                </div>

                <!-- 产品名称输入框 -->
                <div class="form-group">
                    <label for="productName">产品名称:</label>
                    <input type="text" id="productName" placeholder="请输入">
                </div>

                <!-- 工单类型输入框 -->
                <div class="form-group">
                    <label for="workOrderType">工单类型:</label>
                    <input type="text" id="workOrderType" placeholder="请输入">
                </div>

                <!-- 操作按钮 -->
                <button class="btn btn-primary">查询</button>
                <button class="btn btn-secondary">重置</button>
            </div>
        </div>

        <!-- 时间筛选区域 -->
        <div class="date-filter">
            <label for="planDate">生产计划表:</label>
            <input type="date" id="planDate" value="2025-07-01">
            <span class="update-time">更新时间: 2025/06/30 14:15:00</span>
            <span style="background-color: #007bff; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">实时</span>
            <span style="background-color: #6c757d; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">已刷新</span>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>工单号</th>
                        <th>批次号</th>
                        <th>产品名称</th>
                        <th>工单数量(KG)</th>
                        <th>工单类型</th>
                        <th>WAC-S</th>
                        <th>WAC-M</th>
                        <th>WAC-X</th>
                        <th>PCS7</th>
                        <th>整合/接料</th>
                        <th>清洗</th>
                        <th>包装/发货</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 第1行数据 -->
                    <tr>
                        <td>1</td>
                        <td>6133335</td>
                        <td>599040 4NO</td>
                        <td>050004 3472T</td>
                        <td>83</td>
                        <td>常化+回</td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td>01</td>
                    </tr>

                    <!-- 第2行数据 -->
                    <tr>
                        <td>2</td>
                        <td>6133335</td>
                        <td>599040 4NO</td>
                        <td>050004 3472T</td>
                        <td>168</td>
                        <td>常化+回</td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td>02</td>
                    </tr>

                    <!-- 第3行数据 -->
                    <tr>
                        <td>3</td>
                        <td>6133335</td>
                        <td>599040 4NO</td>
                        <td>050004 3472T</td>
                        <td>9</td>
                        <td>常化+回</td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td>03</td>
                    </tr>

                    <!-- 第4行数据 -->
                    <tr>
                        <td>4</td>
                        <td>6133335</td>
                        <td>599040 4NO</td>
                        <td>050004 3472T</td>
                        <td>2</td>
                        <td>常化+回</td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td>04</td>
                    </tr>

                    <!-- 第5行数据 -->
                    <tr>
                        <td>5</td>
                        <td>6133335</td>
                        <td>599040 4NO</td>
                        <td>050004 3472T</td>
                        <td>83</td>
                        <td>常化+回</td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td>05</td>
                    </tr>

                    <!-- 第6行数据 -->
                    <tr>
                        <td>6</td>
                        <td>6133335</td>
                        <td>599040 4NO</td>
                        <td>050004 3472T</td>
                        <td>168</td>
                        <td>常化+回</td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td>06</td>
                    </tr>

                    <!-- 第7行数据 -->
                    <tr>
                        <td>7</td>
                        <td>6133335</td>
                        <td>599040 4NO</td>
                        <td>050004 3472T</td>
                        <td>9</td>
                        <td>常化+回</td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td>07</td>
                    </tr>

                    <!-- 第8行数据 -->
                    <tr>
                        <td>8</td>
                        <td>6133335</td>
                        <td>599040 4NO</td>
                        <td>050004 3472T</td>
                        <td>2</td>
                        <td>常化+回</td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td>08</td>
                    </tr>

                    <!-- 第9行数据 -->
                    <tr>
                        <td>9</td>
                        <td>6133335</td>
                        <td>599040 4NO</td>
                        <td>050004 3472T</td>
                        <td>9</td>
                        <td>常化+回</td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td>09</td>
                    </tr>

                    <!-- 第10行数据 -->
                    <tr>
                        <td>10</td>
                        <td>6133335</td>
                        <td>599040 4NO</td>
                        <td>050004 3472T</td>
                        <td>2</td>
                        <td>常化+回</td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td><button class="status-btn status-not-started">未开始</button></td>
                        <td>10</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页控件 -->
        <div class="pagination">
            <div class="pagination-info">
                共计 140 条
            </div>
            <div class="pagination-controls">
                <a href="#" class="page-btn">1</a>
                <a href="#" class="page-btn active">2</a>
                <a href="#" class="page-btn">&gt;</a>
                <span style="margin-left: 15px; color: #6c757d;">70条/页</span>
                <a href="#" class="page-btn" style="margin-left: 15px;">首页</a>
            </div>
        </div>
    </div>

    <script>
        // 页面交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 查询按钮点击事件
            const queryBtn = document.querySelector('.btn-primary');
            queryBtn.addEventListener('click', function() {
                // 获取搜索条件
                const workOrder = document.getElementById('workOrder').value;
                const batchNo = document.getElementById('batchNo').value;
                const productName = document.getElementById('productName').value;
                const workOrderType = document.getElementById('workOrderType').value;

                console.log('查询条件:', {
                    workOrder,
                    batchNo,
                    productName,
                    workOrderType
                });

                // 这里可以添加实际的查询逻辑
                alert('查询功能已触发，请查看控制台输出');
            });

            // 重置按钮点击事件
            const resetBtn = document.querySelector('.btn-secondary');
            resetBtn.addEventListener('click', function() {
                // 清空所有输入框
                document.getElementById('workOrder').value = '';
                document.getElementById('batchNo').value = '';
                document.getElementById('productName').value = '';
                document.getElementById('workOrderType').value = '';

                console.log('表单已重置');
            });

            // 状态按钮点击事件
            const statusBtns = document.querySelectorAll('.status-btn');
            statusBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const currentStatus = this.textContent;
                    const row = this.closest('tr');
                    const workOrder = row.cells[1].textContent;

                    // 如果当前状态是"未开始"，则变为"开始"
                    if (currentStatus === '未开始') {
                        this.textContent = '开始';
                        this.className = 'status-btn status-started';
                        console.log(`工单 ${workOrder} 状态已从"未开始"变更为"开始"`);
                    } else if (currentStatus === '开始') {
                        // 如果当前状态是"开始"，可以变回"未开始"
                        this.textContent = '未开始';
                        this.className = 'status-btn status-not-started';
                        console.log(`工单 ${workOrder} 状态已从"开始"变更为"未开始"`);
                    } else {
                        console.log(`工单 ${workOrder} 的当前状态: ${currentStatus}`);
                    }
                });
            });

            // 分页按钮点击事件
            const pageBtns = document.querySelectorAll('.page-btn');
            pageBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 移除当前活动状态
                    document.querySelector('.page-btn.active')?.classList.remove('active');

                    // 如果不是导航按钮，添加活动状态
                    if (!['&gt;', '&lt;', '首页', '末页'].includes(this.textContent)) {
                        this.classList.add('active');
                    }

                    console.log('切换到页面:', this.textContent);
                    // 这里可以添加实际的分页逻辑
                });
            });

            // 日期选择器变更事件
            const planDate = document.getElementById('planDate');
            planDate.addEventListener('change', function() {
                console.log('选择的日期:', this.value);
                // 这里可以添加日期筛选的逻辑
            });
        });
    </script>
</body>
</html>